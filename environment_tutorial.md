1. 使用 conda 创建虚拟环境

```bash
cd ./envs
conda create -p $env_name python=3.10
conda activate $env_name
```

2. 安装依赖
    - vllm 环境依赖（可以使用华为镜像源加速：-i https://repo.huaweicloud.com/repository/pypi/simple）

    与 vidur 对应的 vllm 版本为 0.5.0（推测的，这个版本的 pytorch 是对应的）
    ```bash
    pip install torch==2.3.0 torchvision==0.18.0 torchaudio==2.3.0 --index-url https://download.pytorch.org/whl/cu121
    pip install flashinfer-0.1.5+cu121torch2.3-cp310-cp310-linux_x86_64.whl
    pip install vllm==0.5.0
    ```

    最新的 vllm 版本
    ```bash
    pip install torch==2.7.1 torchvision==0.22.1 torchaudio==2.7.1 --index-url https://download.pytorch.org/whl/cu128
    pip install flashinfer-python
    pip install vllm
    ```

    - vidur 环境依赖
    请查看 vidur 项目的 README.md

    - vidur-sarathi 环境安装
    先安装 sarathi 环境，参考 sarathi-serve 项目的 README.md，可以直接使用本地已经下载好的 flashinfer whl 文件安装
    然后安装 vidur 环境，参考 vidur 项目的 README.md

3. 使用指南：
    - 启动 vllm 引擎：
    ```bash
    conda activate ./envs/vllm-*-env
    validation/run_vllm_*.sh
    ```

    - 向 vllm 发送请求：
    ```bash
    conda activate ./envs/vllm-*-env
    python validation/run_vllm.py
    ```

    - 启动 vidur 模拟器：
    ```bash
    conda activate ./envs/vidur-env
    validation/run_vidur.sh
    ```

    - 对比测试：
    ```bash
    conda activate ./envs/vidur-env
    python validation/run_comparison.py
    ```

    - 需要先进行模型 profiling 的话：
    ```bash
    conda activate ./envs/vidur-sarathi-env
    ./run_profiling.sh
    ```


