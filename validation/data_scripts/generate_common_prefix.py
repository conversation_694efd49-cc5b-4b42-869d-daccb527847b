#!/usr/bin/env python3
"""
JSON文件共有前缀提示词生成脚本
分析JSON文件中的文本内容，提取共有的前缀提示词
"""

import argparse
import json
import os
import re
from collections import Counter, defaultdict
from pathlib import Path
from typing import List, Dict, Any, Tuple, Set
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_json_file(file_path: str) -> List[str]:
    """加载JSON文件并返回文本列表"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 处理不同的JSON结构
        if isinstance(data, list):
            # 如果是字符串列表，直接返回
            if all(isinstance(item, str) for item in data):
                return data
            # 如果是对象列表，尝试提取文本字段
            elif all(isinstance(item, dict) for item in data):
                texts = []
                for item in data:
                    # 尝试常见的文本字段名
                    text_fields = ['text', 'content', 'prompt', 'input', 'message', 'body']
                    for field in text_fields:
                        if field in item and isinstance(item[field], str):
                            texts.append(item[field])
                            break
                    else:
                        # 如果没有找到标准字段，取第一个字符串值
                        for value in item.values():
                            if isinstance(value, str) and len(value.strip()) > 0:
                                texts.append(value)
                                break
                return texts
        elif isinstance(data, dict):
            # 如果是单个对象，尝试提取文本
            text_fields = ['text', 'content', 'prompt', 'input', 'message', 'body']
            for field in text_fields:
                if field in data and isinstance(data[field], str):
                    return [data[field]]
            # 如果没有找到标准字段，取第一个字符串值
            for value in data.values():
                if isinstance(value, str) and len(value.strip()) > 0:
                    return [value]
        
        logger.warning(f"无法从 {file_path} 中提取文本内容")
        return []
        
    except Exception as e:
        logger.error(f"加载文件 {file_path} 时出错: {e}")
        return []


def clean_text(text: str) -> str:
    """清理文本，去除多余的空白字符"""
    # 去除多余的空白字符，但保留基本的句子结构
    text = re.sub(r'\s+', ' ', text.strip())
    return text


def extract_sentence_prefixes(texts: List[str], min_length: int = 10, max_length: int = 100) -> List[str]:
    """提取句子级别的前缀"""
    prefixes = []
    
    for text in texts:
        text = clean_text(text)
        if len(text) < min_length:
            continue
            
        # 按句子分割（简单的句号、问号、感叹号分割）
        sentences = re.split(r'[.!?]+', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if min_length <= len(sentence) <= max_length:
                prefixes.append(sentence)
    
    return prefixes


def extract_word_prefixes(texts: List[str], prefix_length: int = 5) -> List[str]:
    """提取词级别的前缀"""
    prefixes = []
    
    for text in texts:
        text = clean_text(text)
        words = text.split()
        
        if len(words) >= prefix_length:
            prefix = ' '.join(words[:prefix_length])
            prefixes.append(prefix)
    
    return prefixes


def extract_character_prefixes(texts: List[str], prefix_length: int = 50) -> List[str]:
    """提取字符级别的前缀"""
    prefixes = []
    
    for text in texts:
        text = clean_text(text)
        if len(text) >= prefix_length:
            prefix = text[:prefix_length]
            prefixes.append(prefix)
    
    return prefixes


def find_common_patterns(prefixes: List[str], min_frequency: int = 2) -> List[Tuple[str, int]]:
    """找出共同的模式"""
    # 统计前缀出现频率
    prefix_counter = Counter(prefixes)
    
    # 过滤出现频率大于等于min_frequency的前缀
    common_prefixes = [(prefix, count) for prefix, count in prefix_counter.items() 
                      if count >= min_frequency]
    
    # 按频率排序
    common_prefixes.sort(key=lambda x: x[1], reverse=True)
    
    return common_prefixes


def find_substring_patterns(texts: List[str], min_length: int = 10, min_frequency: int = 2) -> List[Tuple[str, int]]:
    """找出共同的子字符串模式"""
    substring_counter = Counter()
    
    for text in texts:
        text = clean_text(text)
        # 生成所有可能的子字符串
        for i in range(len(text)):
            for j in range(i + min_length, min(i + 100, len(text) + 1)):  # 限制最大长度为100
                substring = text[i:j].strip()
                if len(substring) >= min_length and not substring.isspace():
                    substring_counter[substring] += 1
    
    # 过滤出现频率大于等于min_frequency的子字符串
    common_substrings = [(substring, count) for substring, count in substring_counter.items() 
                        if count >= min_frequency]
    
    # 按频率排序
    common_substrings.sort(key=lambda x: x[1], reverse=True)
    
    return common_substrings


def analyze_text_patterns(texts: List[str]) -> Dict[str, Any]:
    """分析文本模式"""
    logger.info(f"分析 {len(texts)} 个文本的模式...")
    
    results = {
        'total_texts': len(texts),
        'sentence_prefixes': [],
        'word_prefixes': [],
        'character_prefixes': [],
        'common_substrings': [],
        'statistics': {}
    }
    
    if not texts:
        return results
    
    # 计算基本统计信息
    text_lengths = [len(clean_text(text)) for text in texts]
    results['statistics'] = {
        'avg_length': sum(text_lengths) / len(text_lengths),
        'min_length': min(text_lengths),
        'max_length': max(text_lengths),
        'total_characters': sum(text_lengths)
    }
    
    # 提取句子级别的前缀
    logger.info("提取句子级别的前缀...")
    sentence_prefixes = extract_sentence_prefixes(texts)
    results['sentence_prefixes'] = find_common_patterns(sentence_prefixes, min_frequency=2)
    
    # 提取词级别的前缀
    logger.info("提取词级别的前缀...")
    word_prefixes_5 = extract_word_prefixes(texts, prefix_length=5)
    word_prefixes_10 = extract_word_prefixes(texts, prefix_length=10)
    results['word_prefixes'] = {
        '5_words': find_common_patterns(word_prefixes_5, min_frequency=2),
        '10_words': find_common_patterns(word_prefixes_10, min_frequency=2)
    }
    
    # 提取字符级别的前缀
    logger.info("提取字符级别的前缀...")
    char_prefixes_50 = extract_character_prefixes(texts, prefix_length=50)
    char_prefixes_100 = extract_character_prefixes(texts, prefix_length=100)
    results['character_prefixes'] = {
        '50_chars': find_common_patterns(char_prefixes_50, min_frequency=2),
        '100_chars': find_common_patterns(char_prefixes_100, min_frequency=2)
    }
    
    # 找出共同的子字符串模式
    logger.info("查找共同的子字符串模式...")
    results['common_substrings'] = find_substring_patterns(texts, min_length=15, min_frequency=3)
    
    return results


def generate_report(results: Dict[str, Any], output_file: str):
    """生成分析报告"""
    logger.info(f"生成报告到 {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# JSON文件共有前缀分析报告\n\n")
        
        # 基本统计信息
        f.write("## 基本统计信息\n\n")
        f.write(f"- 总文本数量: {results['total_texts']}\n")
        if results['statistics']:
            stats = results['statistics']
            f.write(f"- 平均文本长度: {stats['avg_length']:.1f} 字符\n")
            f.write(f"- 最短文本长度: {stats['min_length']} 字符\n")
            f.write(f"- 最长文本长度: {stats['max_length']} 字符\n")
            f.write(f"- 总字符数: {stats['total_characters']}\n\n")
        
        # 句子级别的前缀
        f.write("## 句子级别的共有前缀\n\n")
        if results['sentence_prefixes']:
            for i, (prefix, count) in enumerate(results['sentence_prefixes'][:10], 1):
                f.write(f"{i}. **出现次数: {count}**\n")
                f.write(f"   ```\n   {prefix}\n   ```\n\n")
        else:
            f.write("未找到共有的句子前缀。\n\n")
        
        # 词级别的前缀
        f.write("## 词级别的共有前缀\n\n")
        f.write("### 5个词的前缀\n\n")
        if results['word_prefixes']['5_words']:
            for i, (prefix, count) in enumerate(results['word_prefixes']['5_words'][:10], 1):
                f.write(f"{i}. **出现次数: {count}** - `{prefix}`\n")
        else:
            f.write("未找到共有的5词前缀。\n")
        
        f.write("\n### 10个词的前缀\n\n")
        if results['word_prefixes']['10_words']:
            for i, (prefix, count) in enumerate(results['word_prefixes']['10_words'][:10], 1):
                f.write(f"{i}. **出现次数: {count}** - `{prefix}`\n")
        else:
            f.write("未找到共有的10词前缀。\n")

        # 字符级别的前缀
        f.write("\n## 字符级别的共有前缀\n\n")
        f.write("### 50字符前缀\n\n")
        if results['character_prefixes']['50_chars']:
            for i, (prefix, count) in enumerate(results['character_prefixes']['50_chars'][:5], 1):
                f.write(f"{i}. **出现次数: {count}**\n")
                f.write(f"   ```\n   {prefix}\n   ```\n\n")
        else:
            f.write("未找到共有的50字符前缀。\n")

        f.write("### 100字符前缀\n\n")
        if results['character_prefixes']['100_chars']:
            for i, (prefix, count) in enumerate(results['character_prefixes']['100_chars'][:5], 1):
                f.write(f"{i}. **出现次数: {count}**\n")
                f.write(f"   ```\n   {prefix}\n   ```\n\n")
        else:
            f.write("未找到共有的100字符前缀。\n")

        # 共同子字符串
        f.write("## 共同子字符串模式\n\n")
        if results['common_substrings']:
            for i, (substring, count) in enumerate(results['common_substrings'][:10], 1):
                f.write(f"{i}. **出现次数: {count}**\n")
                f.write(f"   ```\n   {substring}\n   ```\n\n")
        else:
            f.write("未找到共同的子字符串模式。\n\n")


def save_prefixes_json(results: Dict[str, Any], output_file: str):
    """保存前缀结果为JSON格式"""
    logger.info(f"保存前缀结果到 {output_file}")

    # 提取最有用的前缀
    extracted_prefixes = {
        'metadata': {
            'total_texts': results['total_texts'],
            'statistics': results['statistics']
        },
        'common_prefixes': []
    }

    # 添加高频的句子前缀
    for prefix, count in results['sentence_prefixes'][:5]:
        extracted_prefixes['common_prefixes'].append({
            'type': 'sentence',
            'text': prefix,
            'frequency': count,
            'length': len(prefix)
        })

    # 添加高频的词前缀
    for prefix, count in results['word_prefixes']['5_words'][:5]:
        extracted_prefixes['common_prefixes'].append({
            'type': 'word_5',
            'text': prefix,
            'frequency': count,
            'length': len(prefix)
        })

    # 添加高频的子字符串
    for prefix, count in results['common_substrings'][:5]:
        extracted_prefixes['common_prefixes'].append({
            'type': 'substring',
            'text': prefix,
            'frequency': count,
            'length': len(prefix)
        })

    # 按频率排序
    extracted_prefixes['common_prefixes'].sort(key=lambda x: x['frequency'], reverse=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(extracted_prefixes, f, indent=2, ensure_ascii=False)


def main():
    parser = argparse.ArgumentParser(description="分析JSON文件中的共有前缀提示词")
    parser.add_argument("input_file", help="输入的JSON文件路径")
    parser.add_argument("--output-dir", default="validation/analysis_results",
                       help="输出目录 (默认: validation/analysis_results)")
    parser.add_argument("--min-frequency", type=int, default=2,
                       help="最小出现频率 (默认: 2)")
    parser.add_argument("--min-length", type=int, default=10,
                       help="最小前缀长度 (默认: 10)")

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input_file):
        logger.error(f"输入文件不存在: {args.input_file}")
        return

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 生成输出文件名
    input_filename = Path(args.input_file).stem
    report_file = output_dir / f"{input_filename}_prefix_analysis.md"
    json_file = output_dir / f"{input_filename}_common_prefixes.json"

    logger.info(f"开始分析文件: {args.input_file}")

    # 加载JSON文件
    texts = load_json_file(args.input_file)
    if not texts:
        logger.error("无法从输入文件中提取文本内容")
        return

    logger.info(f"成功加载 {len(texts)} 个文本")

    # 分析文本模式
    results = analyze_text_patterns(texts)

    # 生成报告
    generate_report(results, str(report_file))

    # 保存JSON结果
    save_prefixes_json(results, str(json_file))

    logger.info("分析完成！")
    logger.info(f"分析报告: {report_file}")
    logger.info(f"JSON结果: {json_file}")

    # 显示简要统计
    print(f"\n=== 分析结果摘要 ===")
    print(f"输入文件: {args.input_file}")
    print(f"文本数量: {results['total_texts']}")
    if results['statistics']:
        print(f"平均长度: {results['statistics']['avg_length']:.1f} 字符")
    print(f"句子前缀: {len(results['sentence_prefixes'])} 个")
    print(f"词前缀(5词): {len(results['word_prefixes']['5_words'])} 个")
    print(f"词前缀(10词): {len(results['word_prefixes']['10_words'])} 个")
    print(f"共同子串: {len(results['common_substrings'])} 个")
    print(f"\n报告文件: {report_file}")
    print(f"JSON文件: {json_file}")


if __name__ == "__main__":
    main()
